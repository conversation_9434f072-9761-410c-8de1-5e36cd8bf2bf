"use client"

import { useState, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, FileText, AlertTriangle, Loader2, ExternalLink, Sparkles, Info } from "lucide-react"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import mongoose from "mongoose"

// Define types for our data
interface Regulation {
  _id: string;
  name: string;
  shortName?: string;
  country?: string;
  link?: string;
}

interface ChemicalRegulation {
  id: string;
  regulation: Regulation;
  smlValue?: string;
  smlUnit?: string;
  notes?: string;
  restrictions?: string;
}

interface AISummary {
  regulationId: string;
  regulationName: string;
  summary: string;
  keyPoints: string[];
  lastUpdated: string;
  confidence: number; // 0-1 value representing AI confidence
}

interface Chemical {
  _id: string;
  chemical_name: string;
  cas_number: string;
  alternative_names?: string[];
  ec_number?: string;
  regulations?: any[];
  source_files?: string[];
  additionalInfo?: Record<string, string>;
  aiSummaries?: AISummary[]; // AI-generated summaries
  created_at: string;
  updated_at: string;
}

interface RecentSearch {
  id: string;
  name: string;
  casNumber: string;
}

export default function ChemicalDetailsPage() {
  const searchParams = useSearchParams()
  const chemicalId = searchParams.get("id")
  const [chemical, setChemical] = useState<Chemical | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [recentSearches, setRecentSearches] = useState<RecentSearch[]>([])
  const [aiSummaries, setAiSummaries] = useState<AISummary[]>([])
  const [aiLoading, setAiLoading] = useState(false)
  const [aiError, setAiError] = useState<string | null>(null)

  // Helper function to validate MongoDB ObjectId
  const isValidObjectId = (id: string): boolean => {
    return mongoose.Types.ObjectId.isValid(id);
  }

  // Function to fetch AI summaries
  const fetchAiSummaries = async (id: string) => {
    try {
      setAiLoading(true)
      setAiError(null)

      // In a real implementation, this would call an actual AI service
      // For now, we'll use mock data
      const response = await fetch(`/api/ai-summary/${id}`)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch AI summaries')
      }

      setAiSummaries(result.data)
    } catch (err: unknown) {
      console.error('Error fetching AI summaries:', err)
      setAiError(err instanceof Error ? err.message : 'Failed to fetch AI summaries')
    } finally {
      setAiLoading(false)
    }
  }

  // Fetch chemical details
  useEffect(() => {
    if (!chemicalId) return

    const fetchChemicalDetails = async () => {
      try {
        setLoading(true)
        setError(null)

        // Validate the chemical ID format before making the API request
        if (!isValidObjectId(chemicalId)) {
          throw new Error('Invalid chemical ID format')
        }

        const response = await fetch(`/api/chemicals/${chemicalId}`)
        const result = await response.json()

        if (!response.ok) {
          throw new Error(result.error || 'Failed to fetch chemical details')
        }

        const chemicalData = result.data
        setChemical(chemicalData)

        // Fetch AI summaries after getting chemical details
        fetchAiSummaries(chemicalId)

        // Get recent searches from session storage and update with current chemical
        try {
          let recentSearchList = []
          const storedSearches = sessionStorage.getItem('recentChemicalSearches')

          if (storedSearches) {
            recentSearchList = JSON.parse(storedSearches)

            // Remove this chemical if it already exists in the list
            recentSearchList = recentSearchList.filter((item: RecentSearch) => item.id !== chemicalData._id)
          }

          // Add current chemical to the beginning of the list
          recentSearchList.unshift({
            id: chemicalData._id,
            name: chemicalData.chemical_name,
            casNumber: chemicalData.cas_number
          })

          // Limit to 5 recent searches
          if (recentSearchList.length > 5) {
            recentSearchList = recentSearchList.slice(0, 5)
          }

          // Save back to session storage
          sessionStorage.setItem('recentChemicalSearches', JSON.stringify(recentSearchList))

          // Update state
          setRecentSearches(recentSearchList)
        } catch (storageError) {
          console.error('Error accessing session storage:', storageError)
        }
      } catch (err: unknown) {
        console.error('Error fetching chemical details:', err)
        setError(err instanceof Error ? err.message : 'Failed to fetch chemical details')
      } finally {
        setLoading(false)
      }
    }

    fetchChemicalDetails()
  }, [chemicalId])

  if (!chemicalId) {
    return (
      <div className="container py-12">
        <Card>
          <CardHeader>
            <CardTitle>Chemical Not Found</CardTitle>
            <CardDescription>No chemical ID was provided</CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild>
              <Link href="/search">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Search
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="container py-12">
        <div className="flex flex-col items-center justify-center py-24">
          <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
          <p className="text-lg text-muted-foreground">Loading chemical details...</p>
        </div>
      </div>
    )
  }

  if (error || !chemical) {
    return (
      <div className="container py-12">
        <Card>
          <CardHeader>
            <CardTitle>Error Loading Chemical</CardTitle>
            <CardDescription>{error || 'Chemical not found'}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild>
              <Link href="/search">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Search
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }



  return (
    <div className="container py-12">
      <div className="mb-6">
        <Button asChild variant="outline" className="mb-4">
          <Link href="/search">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Search
          </Link>
        </Button>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <div className="md:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <div className="flex flex-col space-y-4">
                <div className="flex items-center justify-between">
                  <h1 className="text-3xl font-bold">{chemical.chemical_name}</h1>
                </div>
                <p className="text-muted-foreground">CAS Number: {chemical.cas_number}</p>
                {chemical.alternative_names && chemical.alternative_names.length > 0 && (
                  <div>
                    <p className="text-sm text-muted-foreground">Alternative Names: {chemical.alternative_names.join(', ')}</p>
                  </div>
                )}
                {chemical.ec_number && (
                  <p className="text-sm text-muted-foreground">EC Number: {chemical.ec_number}</p>
                )}
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Content removed - no more risk description */}
            </CardContent>
          </Card>

          <Tabs defaultValue="regulations" className="space-y-4">
            <TabsList className="inline-flex h-auto">
              <TabsTrigger value="regulations" className="h-10">Regulations</TabsTrigger>
              <TabsTrigger value="ai-analysis" className="h-10">AI Analysis</TabsTrigger>
              <TabsTrigger value="details" className="h-10">Chemical Details</TabsTrigger>
              {chemical.additionalInfo && Object.keys(chemical.additionalInfo).length > 0 && (
                <TabsTrigger value="additional" className="h-10">Additional Information</TabsTrigger>
              )}
            </TabsList>

            <TabsContent value="regulations">
              <Card>
                <CardHeader>
                  <CardTitle>Related Regulations</CardTitle>
                  <CardDescription>List of regulations related to {chemical.chemical_name}</CardDescription>
                </CardHeader>
                <CardContent>
                  {chemical.regulations && chemical.regulations.length > 0 ? (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Regulation</TableHead>
                          <TableHead>List Type</TableHead>
                          <TableHead>Limits</TableHead>
                          <TableHead>Notes & Additional Info</TableHead>
                          <TableHead>Restrictions</TableHead>
                          <TableHead></TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {chemical.regulations.map((regulation: any, index: number) => (
                          <TableRow key={index}>
                            <TableCell className="font-medium">
                              {regulation.regulation_name}
                              {regulation.country && (
                                <div className="text-xs text-muted-foreground mt-1">
                                  {regulation.country} - {regulation.region}
                                </div>
                              )}
                            </TableCell>
                            <TableCell>
                              <Badge
                                variant="secondary"
                                className={
                                  regulation.list_type === 'positive' ? 'bg-green-100' :
                                  regulation.list_type === 'negative' ? 'bg-red-100' :
                                  regulation.list_type === 'mixed' ? 'bg-yellow-100' :
                                  'bg-gray-100'
                                }
                              >
                                {regulation.list_type}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              {regulation.limits ? (
                                <div className="space-y-1">
                                  {regulation.limits.sml_value && (
                                    <Badge variant="secondary" className="bg-blue-100">
                                      SML: {regulation.limits.sml_value} {regulation.limits.sml_unit || 'mg/kg'}
                                    </Badge>
                                  )}
                                  {regulation.limits.max_level_percent && (
                                    <Badge variant="secondary" className="bg-purple-100">
                                      Max: {regulation.limits.max_level_percent}%
                                    </Badge>
                                  )}
                                </div>
                              ) : (
                                <span className="text-muted-foreground text-sm">Not specified</span>
                              )}
                            </TableCell>
                            <TableCell>
                              <div className="space-y-2 max-w-xs">
                                {/* Display notes */}
                                {regulation.notes && (
                                  <div>
                                    <div className="text-sm font-medium text-gray-700">Notes:</div>
                                    <div className="text-sm">{regulation.notes}</div>
                                  </div>
                                )}

                                {/* Display additional info from specific_data */}
                                {regulation.specific_data && Object.keys(regulation.specific_data).length > 0 && (
                                  <div>
                                    <div className="text-sm font-medium text-blue-700">Additional Information:</div>
                                    <div className="space-y-1">
                                      {Object.entries(regulation.specific_data).map(([key, value]: [string, any]) => {
                                        // Handle nested additionalInfo object
                                        if (key === 'additionalInfo' && typeof value === 'object' && value !== null) {
                                          return Object.entries(value).map(([nestedKey, nestedValue]: [string, any]) => (
                                            <div key={`${key}-${nestedKey}`} className="text-xs">
                                              <span className="font-medium text-gray-600">{nestedKey}:</span>{' '}
                                              <span className="text-gray-800">
                                                {typeof nestedValue === 'object' ? JSON.stringify(nestedValue) : String(nestedValue)}
                                              </span>
                                            </div>
                                          ));
                                        } else {
                                          // Handle direct key-value pairs
                                          return (
                                            <div key={key} className="text-xs">
                                              <span className="font-medium text-gray-600">{key}:</span>{' '}
                                              <span className="text-gray-800">
                                                {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                                              </span>
                                            </div>
                                          );
                                        }
                                      })}
                                    </div>
                                  </div>
                                )}

                                {/* Show message if no notes or additional info */}
                                {!regulation.notes && (!regulation.specific_data || Object.keys(regulation.specific_data).length === 0) && (
                                  <span className="text-muted-foreground text-sm">No notes or additional information</span>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>
                              {regulation.restrictions ? regulation.restrictions : (
                                <span className="text-muted-foreground text-sm">No restrictions</span>
                              )}
                            </TableCell>
                            <TableCell>
                              <div className="flex gap-2">
                                <Button asChild variant="outline" size="sm">
                                  <Link href={`/regulations/details?id=${regulation.regulation_id}`}>
                                    <FileText className="h-4 w-4 mr-1" />
                                    Details
                                  </Link>
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-muted-foreground">No regulations found for this chemical</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="ai-analysis">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Sparkles className="h-5 w-5 mr-2 text-yellow-500" />
                    AI Analysis
                  </CardTitle>
                  <CardDescription>
                    AI-generated summaries and insights about {chemical.chemical_name} in various regulations
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {aiLoading ? (
                    <div className="flex flex-col items-center justify-center py-8">
                      <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
                      <p className="text-muted-foreground">Analyzing regulations...</p>
                    </div>
                  ) : aiError ? (
                    <div className="text-center py-8">
                      <AlertTriangle className="h-8 w-8 text-yellow-500 mx-auto mb-4" />
                      <p className="text-muted-foreground">{aiError}</p>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-4"
                        onClick={() => fetchAiSummaries(chemicalId!)}
                      >
                        Try Again
                      </Button>
                    </div>
                  ) : aiSummaries.length > 0 ? (
                    <Accordion type="single" collapsible className="w-full">
                      {aiSummaries.map((summary, index) => (
                        <AccordionItem key={index} value={`item-${index}`}>
                          <AccordionTrigger className="hover:no-underline">
                            <div className="flex items-center justify-between w-full pr-4">
                              <div className="flex items-center">
                                <Info className="h-4 w-4 mr-2 text-primary" />
                                <span>
                                  {summary.regulationName}
                                  {summary.confidence >= 0.8 && (
                                    <Badge variant="secondary" className="ml-2 bg-green-100">High Confidence</Badge>
                                  )}
                                </span>
                              </div>
                            </div>
                          </AccordionTrigger>
                          <AccordionContent>
                            <div className="space-y-4 pt-2">
                              <div className="text-sm text-muted-foreground">
                                Last updated: {new Date(summary.lastUpdated).toLocaleDateString()}
                              </div>
                              <div className="text-sm">
                                {summary.summary}
                              </div>
                              {summary.keyPoints.length > 0 && (
                                <div className="mt-4">
                                  <h4 className="text-sm font-medium mb-2">Key Points:</h4>
                                  <ul className="list-disc pl-5 space-y-1">
                                    {summary.keyPoints.map((point, i) => (
                                      <li key={i} className="text-sm">{point}</li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                            </div>
                          </AccordionContent>
                        </AccordionItem>
                      ))}
                    </Accordion>
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-muted-foreground">No AI analysis available for this chemical</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="details">
              <Card>
                <CardHeader>
                  <CardTitle>Chemical Details</CardTitle>
                  <CardDescription>Detailed information about {chemical.chemical_name}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-1">Chemical Name</h3>
                        <p className="text-lg">{chemical.chemical_name}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-1">CAS Number</h3>
                        <p className="text-lg">{chemical.cas_number}</p>
                      </div>
                      {chemical.ec_number && (
                        <div>
                          <h3 className="text-sm font-medium text-muted-foreground mb-1">EC Number</h3>
                          <p className="text-lg">{chemical.ec_number}</p>
                        </div>
                      )}
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-1">Regulations Count</h3>
                        <p className="text-lg">{chemical.regulations?.length || 0}</p>
                      </div>
                    </div>

                    {chemical.alternative_names && chemical.alternative_names.length > 0 && (
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-1">Alternative Names</h3>
                        <p>{chemical.alternative_names.join(', ')}</p>
                      </div>
                    )}

                    {chemical.source_files && chemical.source_files.length > 0 && (
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-1">Source Files</h3>
                        <p>{chemical.source_files.join(', ')}</p>
                      </div>
                    )}

                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Created At</h3>
                      <p>{new Date(chemical.created_at).toLocaleDateString()}</p>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Last Updated</h3>
                      <p>{new Date(chemical.updated_at).toLocaleDateString()}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {chemical.additionalInfo && Object.keys(chemical.additionalInfo).length > 0 && (
              <TabsContent value="additional">
                <Card>
                  <CardHeader>
                    <CardTitle>Additional Information</CardTitle>
                    <CardDescription>Other data related to {chemical.chemical_name}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {Object.entries(chemical.additionalInfo).map(([key, value]: [string, string]) => (
                        <div key={key}>
                          <h3 className="text-sm font-medium text-muted-foreground mb-1">{key}</h3>
                          <p>{value}</p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            )}
          </Tabs>
        </div>

        <div className="md:col-span-1">
          <Card className="sticky top-24">
            <CardHeader>
              <CardTitle>Recent Searches</CardTitle>
              <CardDescription>Your recent chemical searches</CardDescription>
            </CardHeader>
            <CardContent>
              {recentSearches.length > 0 ? (
                <div className="space-y-2">
                  {recentSearches.map((result: RecentSearch) => (
                    <Link key={result.id} href={`/search/details?id=${result.id}`} className="block">
                      <div
                        className={`p-3 rounded-md border hover:bg-muted transition-colors ${result.id === chemical._id ? "bg-muted border-primary" : ""}`}
                      >
                        <div className="font-medium">{result.name}</div>
                        <div className="text-sm text-muted-foreground">CAS: {result.casNumber}</div>
                      </div>
                    </Link>
                  ))}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground">No recent searches found</p>
              )}
              <div className="mt-4">
                <Button asChild variant="outline" size="sm" className="w-full">
                  <Link href="/search">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Search
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

